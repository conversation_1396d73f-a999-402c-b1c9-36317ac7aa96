{"name": "btaskeezalo", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"login": "zmp login", "start": "zmp start", "deploy": "zmp deploy"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@yudiel/react-qr-scanner": "^2.3.1", "@zxing/browser": "^0.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "jotai": "^2.12.1", "jsqr": "^1.4.0", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "zmp-sdk": "latest", "zmp-ui": "latest", "zod": "^4.0.14", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/css": "^0.10.0", "@eslint/js": "^9.31.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "prettier": "3.6.2", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "typescript-eslint": "^8.38.0", "vite": "^5.2.13", "vite-plugin-svgr": "^4.3.0", "zmp-vite-plugin": "latest"}}