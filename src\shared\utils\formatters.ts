// Utility functions for formatting data
export const formatCurrency = (amount: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

export const formatDate = (date: Date | string, format = 'short'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const options: Intl.DateTimeFormatOptions = {}

  switch (format) {
    case 'short':
      options.year = 'numeric'
      options.month = 'short'
      options.day = 'numeric'
      break
    case 'medium':
      options.year = 'numeric'
      options.month = 'long'
      options.day = 'numeric'
      break
    case 'long':
      options.weekday = 'long'
      options.year = 'numeric'
      options.month = 'long'
      options.day = 'numeric'
      break
    default:
      options.year = 'numeric'
      options.month = 'short'
      options.day = 'numeric'
  }

  return new Intl.DateTimeFormat('en-US', options).format(dateObj)
}

export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '')
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`
  }
  return phone
}
