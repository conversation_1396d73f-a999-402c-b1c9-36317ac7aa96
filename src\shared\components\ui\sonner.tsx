import React from 'react'
import { useTheme } from 'next-themes'
import { Toaster as Son<PERSON> } from 'sonner'

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      position="top-center"
      toastOptions={{
        unstyled: true, // <-- this removes all default wrapper styles
      }}
      {...props}
    />
  )
}

export { Toaster }
