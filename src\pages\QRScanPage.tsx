// src/pages/QRScanPage.tsx
import React, { useState, useCallback, useEffect } from 'react'
import { useNavigate, Page } from 'zmp-ui'
import { chooseImage } from 'zmp-sdk/apis'
import { events, EventName } from 'zmp-sdk/apis'
import { Scanner } from '@yudiel/react-qr-scanner'
import jsQR from 'jsqr'
import Gallery from '@/assets/icons/Gallery.svg?react'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import Gift from '@/assets/icons/Gift.svg?react'
import { Separator } from '@/shared/components/ui/separator'

interface QRScanResult {
  text: string
  timestamp: number
}

function QRScanPage() {
  const navigate = useNavigate()
  const [isScanning, setIsScanning] = useState(true) // Start scanning immediately since permission is already granted
  const [scanResult, setScanResult] = useState<QRScanResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [cameraReady, setCameraReady] = useState(false)

  // Handle app lifecycle events
  useEffect(() => {
    const handleAppPaused = () => {
      console.log('App paused, navigating to home')
      navigate('/home', { animate: false, replace: true })
    }

    const handleAppResumed = () => {
      console.log('App resumed, navigating to home')
      navigate('/home', { animate: false, replace: true })
    }

    // Add event listeners
    events.on(EventName.AppPaused, handleAppPaused)
    events.on(EventName.AppResumed, handleAppResumed)

    // Cleanup function to remove event listeners
    return () => {
      events.off(EventName.AppPaused, handleAppPaused)
      events.off(EventName.AppResumed, handleAppResumed)
    }
  }, [navigate])

  // Initialize camera (permission already granted at this point)
  useEffect(() => {
    // Since we only reach this page with granted permission, start scanning immediately
    setIsScanning(true)
    setCameraReady(true)
    setError(null)

    // Cleanup any active streams when component unmounts
    return () => {
      setIsScanning(false)
    }
  }, [])

  const handleBackClick = () =>
    navigate('/home', { animate: false, replace: true })

  const handleScanSuccess = useCallback(
    (result: string) => {
      console.log('QR Scan Success:', result)
      setScanResult({ text: result, timestamp: Date.now() })
      setIsScanning(false)
      setError(null)

      // Navigate to MyRewards page with the scanned code
      navigate('/myrewards', {
        animate: false,
        replace: true,
        state: {
          scannedCode: result,
          fromQRScan: true,
        },
      })
    },
    [navigate]
  )

  const handleScanError = useCallback(
    (_error: unknown) => {
      console.error('QR Scan Error:', _error)
      setTimeout(() => {
        if (!scanResult) setError('Không thể quét mã QR. Vui lòng thử lại.')
      }, 2000)
    },
    [scanResult]
  )

  const handleChooseFromGallery = async () => {
    try {
      const result = await chooseImage({ sourceType: ['album'], count: 1 })
      if (result.filePaths?.length) await scanQRFromImage(result.filePaths[0])
    } catch (err) {
      console.error('Choose image error:', err)
      setError('Không thể mở thư viện ảnh')
    }
  }

  const scanQRFromImage = async (imagePath: string) => {
    console.log('Image path:', imagePath)

    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = async () => {
      console.log('Image loaded successfully')
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!
        canvas.width = img.width
        canvas.height = img.height
        console.log('Canvas size:', canvas.width, canvas.height)
        ctx.drawImage(img, 0, 0)

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        console.log('Image data length:', imageData.data.length)

        // Try with different options
        const code = jsQR(imageData.data, imageData.width, imageData.height, {
          inversionAttempts: 'dontInvert', // Try without inverting
        })

        if (code) {
          console.log('QR code found:', code.data)
          handleScanSuccess(code.data)
          return
        }

        // If not found, try with inversion
        const codeWithInvert = jsQR(
          imageData.data,
          imageData.width,
          imageData.height,
          {
            inversionAttempts: 'attemptBoth',
          }
        )

        if (codeWithInvert) {
          console.log('QR code found with inversion:', codeWithInvert.data)
          handleScanSuccess(codeWithInvert.data)
          return
        }

        // Try resizing the image smaller (sometimes helps with large images)
        const scaleFactor = 0.5
        const smallCanvas = document.createElement('canvas')
        const smallCtx = smallCanvas.getContext('2d')!
        smallCanvas.width = canvas.width * scaleFactor
        smallCanvas.height = canvas.height * scaleFactor
        smallCtx.drawImage(canvas, 0, 0, smallCanvas.width, smallCanvas.height)

        const smallImageData = smallCtx.getImageData(
          0,
          0,
          smallCanvas.width,
          smallCanvas.height
        )
        const smallCode = jsQR(
          smallImageData.data,
          smallImageData.width,
          smallImageData.height,
          {
            inversionAttempts: 'attemptBoth',
          }
        )

        if (smallCode) {
          console.log('QR code found in smaller image:', smallCode.data)
          handleScanSuccess(smallCode.data)
          return
        }

        console.log('No QR code found in image after all attempts')
        setError('Không tìm thấy mã QR trong ảnh')
      } catch (err) {
        console.error('Image QR decode error:', err)
        setError('Không tìm thấy mã QR trong ảnh')
      }
    }

    img.onerror = (err) => {
      console.error('Image load error:', err)
      setError('Không thể tải ảnh')
    }

    img.src = imagePath
  }

  const maxSize = 256
  const maxTop = 200

  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight

  const frameWidth = Math.min(screenWidth * 0.6, maxSize)
  const frameHeight = frameWidth // Keep it square

  const frameLeft = (screenWidth - frameWidth) / 2 // Center horizontally
  const frameTop = Math.min(screenHeight * 0.2, maxTop) // 20% of height, max 200

  const shouldShowScanner = isScanning && cameraReady && !scanResult && !error

  return (
    <Page className="relative text-white bg-black flex flex-col min-h-screen overflow-hidden">
      <div className="absolute inset-0 z-0">
        {shouldShowScanner && (
          <Scanner
            onScan={(barcodes) => {
              if (barcodes.length > 0) {
                const barcode = barcodes[0]
                const { x, y, width, height } = barcode.boundingBox
                const qrCenterX = x + width / 2
                const qrCenterY = y + height / 2
                if (
                  qrCenterX >= frameLeft &&
                  qrCenterX <= frameLeft + frameWidth &&
                  qrCenterY >= frameTop &&
                  qrCenterY <= frameTop + frameHeight
                ) {
                  handleScanSuccess(barcode.rawValue)
                }
              }
            }}
            onError={handleScanError}
            constraints={{
              facingMode: 'environment',
              width: { ideal: window.innerHeight },
              height: { ideal: window.innerWidth },
              aspectRatio: { ideal: window.innerHeight / window.innerWidth },
            }}
            components={{
              torch: false,
              zoom: false,
              onOff: false,
              finder: false,
            }}
            styles={{
              container: {
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0,
              },
              video: {
                objectFit: 'contain',
                width: '100%',
                height: '100%', // Mirror effect like front camera
              },
            }}
          />
        )}
        {!shouldShowScanner && <div className="w-full h-full bg-white" />}
      </div>

      {/* Overlay with hole in the middle for QR frame */}
      <div className="absolute inset-0 pointer-events-none z-10 ">
        <div
          className="absolute z-10"
          style={{
            top: frameTop - 2,
            left: frameLeft - 2,
            width: frameWidth + 4,
            height: frameHeight + 4,
          }}
        >
          {/* Top-left corner */}
          <div className="absolute top-0 left-0 w-14 h-14 border-t-[7px] border-l-[7px] border-[#FF8228] rounded-tl-[40px]" />

          {/* Top-right corner */}
          <div className="absolute top-0 right-0 w-14 h-14 border-t-[7px] border-r-[7px] border-[#FF8228] rounded-tr-[40px]" />

          {/* Bottom-left corner */}
          <div className="absolute bottom-0 left-0 w-14 h-14 border-b-[7px] border-l-[7px] border-[#FF8228] rounded-bl-[40px]" />

          {/* Bottom-right corner */}
          <div className="absolute bottom-0 right-0 w-14 h-14 border-b-[7px] border-r-[7px] border-[#FF8228] rounded-br-[40px]" />
        </div>

        {/* The dimmed background outside the frame */}
        <div
          className="absolute inset-0 rounded-[40px]"
          style={{
            boxShadow: '0 0 0 9999px rgba(0,0,0,0.7)',
            top: frameTop,
            left: frameLeft,
            width: frameWidth,
            height: frameHeight,
            pointerEvents: 'none',
          }}
        />
      </div>

      <div className="font-montserrat relative z-20 flex flex-col w-full h-full pt-8 pb-8 px-4">
        {/* Top Navigation */}
        <div className="flex items-start justify-start pointer-events-auto z-20">
          <button
            onClick={handleBackClick}
            className="bg-[#F8F9FB] rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
          >
            <LeftArrow className="scale-110 -translate-x-0.5" />
          </button>
        </div>

        {/* Title */}
        <div className="mt-14 h-xs:mt-1 h-sm:mt-3 h-md:mt-8 h-lg:mt-8 h-xl:mt-10 h-2xl:mt-12">
          <h1 className="text-white text-[16px] h-xs:text-[14px] font-bold text-center">
            Quét mã để lưu trữ ưu đãi
          </h1>
        </div>

        {/* Bottom Actions */}
        <div className="flex flex-col gap-4 mt-[350px] h-xs:mt-[250px] h-sm:mt-[280px] h-md:mt-[310px] h-lg:mt-[310px] h-xl:mt-[310px] h-2xl:mt-[310px] h-full">
          {/* Gallery Button */}
          <button
            onClick={handleChooseFromGallery}
            className="flex flex-col items-center justify-center gap-3"
          >
            <div className="w-12 h-12 flex items-center justify-center">
              <Gallery />
            </div>
            <span className="text-white font-semibold text-xs">Thư viện</span>
          </button>

          {/* Status Text */}
          <div className="mx-auto mt-auto w-full max-w-lg bg-[#FFFFFF] rounded-2xl p-3 flex items-center space-x-3">
            <Gift className="w-8 h-8" />
            <Separator orientation="vertical" className="h-8 bg-[#EBEFF6]" />
            <span className="text-[#A5ACB6] font-medium text-[14px]">
              Bạn hiện chưa có ưu đãi nào
            </span>
          </div>
        </div>
      </div>
    </Page>
  )
}

export default QRScanPage
