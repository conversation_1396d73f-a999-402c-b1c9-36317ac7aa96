.page {
  padding: 16px 16px 96px 16px;
}

.section-container {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.zaui-list-item {
  cursor: pointer;
}

.pt-safe {
  padding-top: calc(env(safe-area-inset-top) + 2px);
}
.pb-safe {
  padding-bottom: calc(env(safe-area-inset-bottom) + 2px);
}

/* Fallback if inset is zero */
body.no-safe-top .pt-safe {
  padding-top: 30px;
}
body.no-safe-bottom .pb-safe {
  padding-bottom: 10px;
}

/* Sonner toaster */
[data-sonner-toaster][data-y-position='top'] {
  top: calc(env(safe-area-inset-top, 40px) + 2px) !important;
}
body.no-safe-top [data-sonner-toaster][data-y-position='top'] {
  top: 30px !important;
}

@font-face {
  font-family: 'Montserrat';
  src: url('../assets/fonts/Montserrat-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('../assets/fonts/Montserrat-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: bold;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('../assets/fonts/Montserrat-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: semibold;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('../assets/fonts/Montserrat-MediumItalic.woff2') format('woff2');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}
