import React, { useState } from 'react'
import { useNavigate, Page } from 'zmp-ui'
import { closeApp, authorize } from 'zmp-sdk/apis'
import IntroBG from '@/assets/images/IntroBG.svg?react'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import IntroCarouselSegment from '@/shared/components/intro/IntroCarouselSegment'
import PermissionSegment from '@/shared/components/intro/PermissionSegment'

export interface AppError extends Error {
  code: number
}

function IntroPage() {
  const navigate = useNavigate()
  const [showPermissions, setShowPermissions] = useState(false)

  const handleFinishIntro = () => {
    setShowPermissions(true)
  }

  const handlePermissionComplete = async () => {
    try {
      const data = await authorize({
        scopes: ['scope.userInfo', 'scope.userPhonenumber'],
      })
      console.log(data['scope.userLocation']) // true if user granted location
      console.log(data['scope.userPhonenumber']) // true if user granted phone number
      navigate('/signup', { animate: false })
    } catch (error) {
      const code = (error as AppError).code
      if (code === -201) {
        console.log('User denied permission')
      } else {
        console.log('Other error')
      }
    }
  }

  const handleBackClick = () => {
    if (showPermissions) {
      // If on permission step, go back to intro
      setShowPermissions(false)
    } else {
      // If already on intro step, exit app
      closeApp()
    }
  }

  return (
    <Page className="relative text-black flex flex-col items-center justify-center min-h-screen overflow-hidden">
      {/* SVG Background */}
      <div className="absolute inset-0 w-full h-full">
        <IntroBG className="w-full h-full" style={{ objectFit: 'fill' }} />
      </div>

      {/* Content */}
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe pb-safe px-2">
        {/* Top Navigation */}
        <div className="flex items-start justify-start pointer-events-auto z-20">
          <button
            onClick={handleBackClick}
            className="bg-[#F8F9FB] rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
          >
            <LeftArrow className="scale-110 -translate-x-0.5" />
          </button>
        </div>

        {/* Conditional Content */}
        {showPermissions ? (
          <PermissionSegment onPermissionComplete={handlePermissionComplete} />
        ) : (
          <IntroCarouselSegment onFinishIntro={handleFinishIntro} />
        )}
      </div>
    </Page>
  )
}

export default IntroPage
