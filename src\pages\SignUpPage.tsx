import React, { useState } from 'react'
import { useN<PERSON>gate, Page } from 'zmp-ui'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import Phone from '@/assets/icons/Phone.svg?react'
import { Eye, EyeOff } from 'lucide-react'
import { Separator } from '@/shared/components/ui/separator'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shared/components/ui/avatar'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/shared/components/ui/form'
import { Input } from '@/shared/components/ui/input'
import { useForm } from 'react-hook-form'
import { closeApp } from 'zmp-sdk/apis'

function SignUpPage() {
  const navigate = useNavigate()
  const [showPassword, setShowPassword] = useState(false)
  const [confirmPasswordTouched, setConfirmPasswordTouched] = useState(false)

  const form = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const password = form.watch('password')
  const confirmPassword = form.watch('confirmPassword')
  const passwordsMatch = password === confirmPassword
  const showValidation = confirmPasswordTouched && confirmPassword.length > 0

  const handleBackClick = () => {
    closeApp()
  }

  const handleSignUp = () => {
    if (password && confirmPassword && passwordsMatch) {
      navigate('/home', { animate: false, replace: true })
      // Handle sign up logic
    }
  }

  const getButtonStyles = () => {
    if (!password || !confirmPassword || !passwordsMatch) {
      return {
        backgroundColor: '#E3E7ED',
        color: '#FFFFFF',
      }
    }
    return {
      backgroundColor: '#E4EAF0',
      color: '#1BB55C',
    }
  }

  return (
    <Page className="relative text-black flex flex-col items-center justify-center min-h-screen overflow-hidden bg-white">
      {/* Content */}
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe pb-safe">
        {/* Top Navigation */}
        <div className="mb-8 h-xs:mb-2 h-sm:mb-4 h-md:mb-4 h-lg:mb-4 h-xl:mb-5 h-2xl:mb-6">
          <div className="flex items-center px-2">
            <button
              onClick={handleBackClick}
              className="rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
            >
              <LeftArrow className="scale-110 h-xs:scale-100 h-sm:scale-100 -translate-x-0.5" />
            </button>
            <span className="text-[18px] h-xs:text-[16px] h-sm:text-[16px] font-bold text-[#212935]">
              Đăng ký tài khoản
            </span>
          </div>
          <Separator className="mt-3 h-xs:mt-0.5 h-sm:mt-1 h-md:mt-2 h-lg:mt-2 h-xl:mt-2.5 bg-[#EBEFF6]" />
        </div>

        <div className="w-full max-w-lg flex flex-col mx-auto">
          {/* Description Text */}
          <div className="mb-8 pl-4 pr-6 h-xs:mb-2 h-sm:mb-4 h-md:mb-4 h-lg:mb-4 h-xl:mb-5 h-2xl:mb-6 max-w-lg">
            <p className="text-[14px] text-[#3C4B61] leading-relaxed font-medium">
              Bạn có thể đăng ký tài khoản bTaskee nhanh chóng tại đây dựa trên{' '}
              <span className="font-semibold text-[#3C4B61]">
                số điện thoại
              </span>{' '}
              đang sử dụng cho tài khoản Zalo này
            </p>
          </div>

          {/* User Info Card */}
          <div className="flex items-center space-x-4 mb-12 h-xs:mb-2 h-sm:mb-4 h-md:mb-4 h-lg:mb-6 h-xl:mb-6 h-2xl:mb-7 mx-4 bg-[#F7FBFF] py-5 h-xs:py-3 h-sm:py-3 px-5 rounded-xl max-w-lg">
            <Avatar className="w-16 h-16 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10">
              <AvatarImage src="https://github.com/shadcn.png" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="text-[18px] h-xs:text-[16px] h-sm:text-[16px] font-bold text-[#374458] mb-2">
                Hoàng Phương
              </h3>
              <div className="flex items-center space-x-2">
                <Phone className="w-3.5 h-3.5" />
                <span className="text-[16px] h-xs:text-[14px] h-sm:text-[14px] text-[#FF8228] font-medium">
                  0971 552 942
                </span>
              </div>
            </div>
          </div>

          {/* Form Fields */}
          <div className="flex-1 space-y-6 h-xs:space-y-2 h-sm:space-y-4 h-md:space-y-4 mx-4 max-w-lg">
            <Separator className="bg-[#EBEFF6] mb-12 h-xs:mb-2 h-sm:mb-4 h-md:mb-4 h-lg:mb-6 h-xl:mb-6 h-2xl:mb-7" />

            <Form {...form}>
              <form className="space-y-10 h-xs:space-y-2 h-sm:space-y-4 h-md:space-y-4 h-lg:space-y-6 h-xl:space-y-6 h-2xl:space-y-7">
                {/* Password Field */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[14px] font-medium text-[#3C4B61]">
                        Mật khẩu
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="text"
                            maxLength={12}
                            className="px-4 py-6 text-[14px] border-[#EBEFF6] focus:ring-2 focus:border-transparent rounded-lg shadow-none"
                            placeholder="Nhập mật khẩu"
                            value={
                              showPassword
                                ? field.value
                                : '*'.repeat(field.value.length)
                            }
                            onChange={(e) => {
                              if (showPassword) {
                                field.onChange(e.target.value.slice(0, 12))
                              } else {
                                // Handle masked input
                                const newLength = e.target.value.length
                                const oldLength = field.value.length

                                if (newLength > oldLength) {
                                  // Adding characters
                                  const newChars =
                                    e.target.value.slice(oldLength)
                                  const cleanChars = newChars.replace(/\*/g, '')
                                  field.onChange(
                                    (field.value + cleanChars).slice(0, 12)
                                  )
                                } else if (newLength < oldLength) {
                                  // Removing characters
                                  field.onChange(
                                    field.value.slice(0, newLength)
                                  )
                                }
                              }
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1"
                          >
                            {showPassword ? (
                              <Eye className="w-5 h-5 text-gray-400" />
                            ) : (
                              <EyeOff className="w-5 h-5 text-gray-400" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Confirm Password Field */}
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[14px] font-medium text-[#3C4B61]">
                        Xác nhận mật khẩu
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="text"
                            maxLength={12}
                            className="px-4 py-6 text-[14px] border-[#EBEFF6] focus:ring-2 focus:border-transparent rounded-lg shadow-none"
                            placeholder="Nhập mật khẩu"
                            value={
                              showPassword
                                ? field.value
                                : '*'.repeat(field.value.length)
                            }
                            onChange={(e) => {
                              if (showPassword) {
                                field.onChange(e.target.value.slice(0, 12))
                              } else {
                                // Handle masked input
                                const newLength = e.target.value.length
                                const oldLength = field.value.length

                                if (newLength > oldLength) {
                                  // Adding characters
                                  const newChars =
                                    e.target.value.slice(oldLength)
                                  const cleanChars = newChars.replace(/\*/g, '')
                                  field.onChange(
                                    (field.value + cleanChars).slice(0, 12)
                                  )
                                } else if (newLength < oldLength) {
                                  // Removing characters
                                  field.onChange(
                                    field.value.slice(0, newLength)
                                  )
                                }
                              }
                              setConfirmPasswordTouched(true)
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1"
                          >
                            {showPassword ? (
                              <Eye className="w-5 h-5 text-gray-400" />
                            ) : (
                              <EyeOff className="w-5 h-5 text-gray-400" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      {showValidation && !passwordsMatch && (
                        <p className="text-[#EB343F] text-[12px] mt-2 text-right font-medium">
                          Mật khẩu không khớp
                        </p>
                      )}
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>

        {/* Sign Up Button */}
        <div className="mt-auto px-4 w-full max-w-lg mx-auto mb-4">
          <button
            onClick={handleSignUp}
            disabled={!password || !confirmPassword || !passwordsMatch}
            className="w-full font-semibold py-4 px-6 rounded-lg transition-colors duration-200 text-[16px]"
            style={getButtonStyles()}
          >
            Đăng ký
          </button>
        </div>
      </div>
    </Page>
  )
}

export default SignUpPage
