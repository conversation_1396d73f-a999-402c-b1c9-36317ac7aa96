import React, { useState, useEffect } from 'react'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from '@/shared/components/ui/carousel'
import { ArrowRight } from 'lucide-react'
import IntroCarousel1 from '@/assets/images/IntroCarousel1.svg?react'
import IntroCarousel2 from '@/assets/images/IntroCarousel2.svg?react'
import IntroCarousel3 from '@/assets/images/IntroCarousel3.svg?react'
import IntroCarousel4 from '@/assets/images/IntroCarousel4.svg?react'

interface IntroCarouselSegmentProps {
  onFinishIntro: () => void
}

function IntroCarouselSegment({ onFinishIntro }: IntroCarouselSegmentProps) {
  const [api, setApi] = useState<CarouselApi | null>(null)
  const [selectedIndex, setSelectedIndex] = useState(0)

  useEffect(() => {
    if (!api) return

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap())
    }

    onSelect() // set initial index
    api.on('select', onSelect)

    return () => {
      api.off('select', onSelect)
    }
  }, [api])

  const carouselSlides = [
    {
      Component: IntroCarousel1,
      title: 'Đặt lịch nhanh chóng',
      description:
        'Thao tác 60 giây trên ứng dụng, có ngay người nhận việc sau 60 phút.',
    },
    {
      Component: IntroCarousel2,
      title: 'Giá cả rõ ràng',
      description:
        'Giá dịch vụ được hiển thị rõ ràng trên ứng dụng bTaskee. Bạn không phải trả thêm bất kỳ khoản chi phí nào.',
    },
    {
      Component: IntroCarousel3,
      title: 'Đặt lịch nhanh chóng',
      description:
        'Với 14 dịch vụ tiện ích, bTaskee sẵn sàng hỗ trợ mọi nhu cầu việc nhà của bạn.',
    },
    {
      Component: IntroCarousel4,
      title: 'An toàn tối đa',
      description:
        'Người làm uy tín, có hồ sơ lý lịch rõ ràng và được Công ty giám sát trong suốt quá trình làm việc.',
    },
  ]

  const handleNextClick = () => {
    if (selectedIndex === 3) {
      onFinishIntro()
    } else {
      api?.scrollNext()
    }
  }

  return (
    <div className="flex flex-col h-full w-full">
      {/* Main content section */}
      <div className="flex-1 flex flex-col items-center justify-center h-sm:space-y-8 h-md:space-y-12 h-lg:space-y-14 h-xl:space-y-16 h-2xl:space-y-14 h-3xl:space-y-16 h-4xl:space-y-16 h-5xl:space-y-16 h-6xl:space-y-20">
        <Carousel className="w-full max-w-lg" setApi={setApi}>
          <CarouselContent>
            {carouselSlides.map(({ Component, title, description }, index) => (
              <CarouselItem
                key={index}
                className="flex flex-col items-center justify-center space-y-4"
              >
                <div className="w-[80%]">
                  <Component className="w-full h-full" />
                </div>
                <div className="text-center px-4 h-lg:pt-8 h-xl:pt-10 h-2xl:pt-10 h-3xl:pt-14 h-4xl:pt-16 h-5xl:pt-16 h-6xl:pt-20">
                  <h2 className="text-[18px] font-bold text-[#FF8228]">
                    {title}
                  </h2>
                  <p className="text-[#374458] mt-3 font-medium text-[14px]">
                    {description}
                  </p>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Progress Indicators */}
        <div className="flex justify-center mt-4 space-x-2 translate-y-2 h-lg:translate-y-2 h-xl:translate-y-4 h-2xl:translate-y-6 h-3xl:translate-y-8 h-4xl:translate-y-10 h-5xl:translate-y-12 h-6xl:translate-y-14">
          {[0, 1, 2, 3].map((i) => (
            <div
              key={i}
              className={`h-1.5 rounded-full transition-all duration-300 w-10 ${
                selectedIndex === i ? 'bg-[#FF8228]' : 'bg-[#FFF3EA]'
              }`}
            />
          ))}
        </div>
      </div>

      <div className="py-4">
        <button
          onClick={handleNextClick}
          className={`mt-4 px-6 py-4 flex items-center justify-center
    ${
      selectedIndex === 3
        ? 'rounded-xl w-full max-w-lg bg-[#E4EAF0] text-[#1BB55C] text-[16px] font-semibold mx-auto'
        : 'bg-[#1BB55C] text-white w-[56px] h-[56px] rounded-full mx-auto'
    }`}
        >
          {selectedIndex === 3 ? (
            'Bắt đầu ngay'
          ) : (
            <div className="scale-125">
              <ArrowRight />
            </div>
          )}
        </button>
      </div>
    </div>
  )
}

export default IntroCarouselSegment
