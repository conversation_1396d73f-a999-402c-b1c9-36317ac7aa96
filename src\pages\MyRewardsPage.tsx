// src\pages\MyRewardsPage.tsx
import React, { useState, useEffect, useRef } from 'react'
import { Page, useNavigate } from 'zmp-ui'
import { useLocation } from 'react-router-dom'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import ToastSuccess from '@/assets/icons/ToastSuccess.svg?react'
import ToastError from '@/assets/icons/ToastError.svg?react'
import NoVoucher from '@/assets/icons/NoVoucher.svg?react'
import VoucherImg from '@/assets/icons/Voucher.webp'
import VoucherBGNew from '@/assets/images/VoucherBGNew.svg?react'
import VoucherBGOld from '@/assets/images/VoucherBGOld.svg?react'
import { Input } from '@/shared/components/ui/input'
import { toast } from 'sonner'
import { Toaster } from '@/shared/components/ui/sonner'
import { ScrollArea } from '@/shared/components/ui/scroll-area'

// Voucher type now includes variant to choose background
type Voucher = {
  title: string
  description: string
  expiryDate: string
  variant: 'new' | 'old'
}

// Define the state interface for navigation
interface NavigationState {
  scannedCode?: string
  fromQRScan?: boolean
}

function MyRewardsPage() {
  const navigate = useNavigate()
  const location = useLocation()
  const [promoCode, setPromoCode] = useState<string>('')
  const [vouchers, setVouchers] = useState<Voucher[]>([])
  const [isInputFocused, setIsInputFocused] = useState<boolean>(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const hiddenFocusRef = useRef<HTMLButtonElement>(null)
  const state = location.state as { fromVoucherInput?: boolean }
  const processedCodeRef = useRef<string | null>(null)

  useEffect(() => {
    if (state?.fromVoucherInput) {
      // Simulate a user gesture by triggering a click
      hiddenFocusRef.current?.click()
    }
  }, [state])

  const handleFocusClick = () => {
    inputRef.current?.focus()
  }

  // Mock valid promo codes (case-sensitive)
  const validPromoCodes = [
    'https://qr.me-qr.com/psphAwFW',
    'DISCOUNT50',
    'WELCOME10',
  ]

  // Handle QR scan result on component mount
  useEffect(() => {
    const navState = location.state as NavigationState
    if (
      navState?.fromQRScan &&
      navState.scannedCode &&
      processedCodeRef.current !== navState.scannedCode
    ) {
      processedCodeRef.current = navState.scannedCode
      handlePromoCodeSubmission(navState.scannedCode.trim())
      window.history.replaceState({}, '', window.location.pathname)
    }
  }, [location])

  const handleBackClick = () => {
    navigate('/home', { animate: false, replace: true })
  }

  const handlePromoCodeSubmission = (code: string) => {
    if (validPromoCodes.includes(code)) {
      toast(
        <div className="font-montserrat flex items-center gap-2 text-[#1BB55C] text-[14px]">
          <ToastSuccess className="w-5 h-5 shrink-0" />
          <span>Lưu mã ưu đãi thành công</span>
        </div>,
        {
          position: 'top-center',
          unstyled: true,
          className: 'bg-[#E8F8EF] rounded-xl px-4 py-3 w-full',
        }
      )
      setVouchers((prev) => [
        ...prev,
        {
          title: 'Giảm 10% cho hoá đơn 200.000đ',
          description: 'Khi đặt lịch Vệ sinh sofa, Đệm, Rèm, Thảm, Rèm, Thảm',
          expiryDate: '31/07/2025',
          variant: 'old',
        },
      ])
    } else {
      toast(
        <div className="font-montserrat flex items-center gap-2 text-[#EB343F] text-[14px]">
          <ToastError className="w-5 h-5 shrink-0" />
          <span>Mã ưu đãi không hợp lệ</span>
        </div>,
        {
          position: 'top-center',
          unstyled: true,
          className: 'bg-[#FDEBEC] rounded-xl px-4 py-3 w-full',
        }
      )
    }
  }

  const handleSavePromoCode = () => {
    const code = promoCode.trim()
    setPromoCode('')
    handlePromoCodeSubmission(code)
  }

  return (
    <Page className="relative text-black flex flex-col items-center justify-center min-h-screen overflow-hidden bg-white">
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe">
        {/* Top Navigation */}
        <div className="mb-3 h-xs:mb-2 flex items-center px-2">
          <button
            onClick={handleBackClick}
            className="rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
          >
            <LeftArrow className="scale-110 h-xs:scale-100 h-sm:scale-100 -translate-x-0.5" />
          </button>
          <span className="text-[18px] h-xs:text-[16px] h-sm:text-[16px] font-bold text-[#212935]">
            Ưu đãi của tôi
          </span>
        </div>

        {/* Content */}
        <div className="flex-1 bg-[#FBFCFF] pb-4 flex flex-col overflow-hidden">
          {/* Promo Code Input Section - Fixed height */}
          <div className="mb-5 flex items-center mt-4 flex-shrink-0 px-4">
            <div className="h-xs:h-8 h-sm:h-8 flex-1 bg-[#FFFFFF] border border-[#EBEFF6] rounded-xl flex items-center px-2 py-2 mx-auto max-w-lg">
              <img
                src={VoucherImg}
                alt="Voucher"
                className="w-7 h-7 h-xs:w-4 h-xs:h-4 h-sm:w-4 h-sm:h-4 ml-2"
              />

              <button
                ref={hiddenFocusRef}
                onClick={handleFocusClick}
                style={{ display: 'none' }}
              >
                Focus
              </button>

              <Input
                ref={inputRef}
                placeholder="Nhập mã khuyến mãi"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                onFocus={() => setIsInputFocused(true)}
                onBlur={() => setIsInputFocused(false)}
                className="flex-1 border-none shadow-none h-xs:text-[13px] h-sm:text-[13px]"
              />

              <button
                onClick={handleSavePromoCode}
                disabled={!promoCode.trim()}
                className="
    px-4 py-2 h-xs:py-0.5 h-xs:px-3 h-sm:py-0.5 h-sm:px-3 rounded-xl font-bold text-[12px] transition-colors duration-200 shadow-none
    bg-[#1BB55C] text-white
    disabled:bg-[#EBEFF6]
    disabled:text-white
    disabled:opacity-100
    disabled:cursor-not-allowed
  "
              >
                Lưu
              </button>
            </div>
          </div>

          {/* Vouchers List or Empty State - Flexible scrollable area */}
          <div className="flex-1 overflow-hidden px-4">
            {vouchers.length === 0 && !isInputFocused ? (
              <div className="h-full flex flex-col items-center justify-center pb-40 h-xs:pb-20 h-sm:pb-20">
                <NoVoucher className="w-44 h-44 mb-4 h-xs:w-32 h-xs:h-32 h-xs:mb-2 h-sm:w-32 h-sm:h-32 h-sm:mb-2" />
                <p className="text-gray-500 text-[14px] font-medium">
                  Chưa có ưu đãi
                </p>
              </div>
            ) : (
              <ScrollArea className="h-full mx-auto max-w-lg">
                <div className="space-y-5 h-xs:space-y-3 h-sm:space-y-3">
                  {vouchers.map((voucher, index) => (
                    <div
                      key={index}
                      className="relative overflow-hidden rounded-lg"
                      style={{ aspectRatio: '358/182' }}
                    >
                      {/* Background SVG */}
                      {voucher.variant === 'new' ? (
                        <VoucherBGNew className="absolute inset-0 w-full h-full object-cover" />
                      ) : (
                        <VoucherBGOld className="absolute inset-0 w-full h-full object-cover" />
                      )}
                      {/* Content Overlay */}
                      <div className="relative z-10 flex flex-col h-full px-3">
                        {/* Top section - 27.45% height */}
                        <div className="flex items-center justify-between h-[27.45%]">
                          <span className="text-[14px] font-medium text-[#212935] ml-9 h-xs:ml-6 h-xs:text-[13px] h-sm:ml-8 h-sm:text-[13px] h-md:ml-8 h-lg:ml-8 h-xl:ml-8 h-2xl:ml-8">
                            bTaskee
                          </span>

                          {voucher.variant === 'new' && (
                            <span
                              className="
        bg-[#FF3B30] text-white
        text-[9px] italic
        px-2.5 py-1 rounded-full -translate-y-1 h-xs:-translate-y-0
      "
                            >
                              NEW
                            </span>
                          )}
                        </div>

                        {/* Bottom section - 72.55% height */}
                        <div className="flex flex-col justify-between h-[72.55%] py-4 h-xs:py-1 h-sm:py-3 h-md:py-3">
                          <h3 className="font-bold text-[#1BB55C] text-[16px] h-xs:text-[14px] h-sm:text-[14px]">
                            {voucher.title}
                          </h3>
                          <p className="text-[#636F81] font-medium text-[14px] line-clamp-2 h-xs:text-[13px] h-sm:text-[13px]">
                            {voucher.description}
                          </p>
                          <span className="font-medium text-[12px] text-[#A5ACB6]">
                            HSD: {voucher.expiryDate}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>

        <Toaster />
      </div>
    </Page>
  )
}

export default MyRewardsPage
