const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  darkMode: ['selector', '[zaui-theme="dark"]'],
  content: ['./src/**/*.{js,jsx,ts,tsx,vue}'],
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar')({ nocompatible: true }),
  ],
  theme: {
    extend: {
      screens: {
        'h-xs': { raw: '(max-height: 600px)' },
        'h-sm': { raw: '(min-height: 601px) and (max-height: 699px)' },
        'h-md': { raw: '(min-height: 701px) and (max-height: 739px)' },
        'h-lg': { raw: '(min-height: 740px) and (max-height: 779px)' },
        'h-xl': { raw: '(min-height: 780px) and (max-height: 799px)' },
        'h-2xl': { raw: '(min-height: 800px) and (max-height: 839px)' },
        'h-3xl': { raw: '(min-height: 840px) and (max-height: 889px)' },
        'h-4xl': { raw: '(min-height: 890px) and (max-height: 939px)' },
        'h-5xl': { raw: '(min-height: 940px) and (max-height: 989px)' },
        'h-6xl': { raw: '(min-height: 990px)' },
      },
      fontFamily: {
        mono: ['Roboto Mono', 'monospace'],
        montserrat: ['Montserrat', ...defaultTheme.fontFamily.sans],
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
}
