// src/pages/HomePage.tsx
import React, { useEffect, useState, useCallback } from 'react'
import { useNavigate, Page } from 'zmp-ui'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shared/components/ui/avatar'
import HomeBG from '@/assets/images/HomeBG.svg?react'
import QRScan from '@/assets/icons/QRScan.svg?react'
import VoucherImg from '@/assets/icons/Voucher.webp'
import MapHome from '@/assets/icons/MapHome.svg?react'
import MapIcon from '@/assets/icons/MapIcon.svg?react'
import Cleaning from '@/assets/icons/Cleaning.svg?react'
import OfficeCleaning from '@/assets/icons/OfficeCleaning.svg?react'
import Housekeeping from '@/assets/icons/Housekeeping.svg?react'
import WashingMachine from '@/assets/icons/WashingMachine.svg?react'
import MonthlyCleaning from '@/assets/icons/MonthlyCleaning.svg?react'
import ChildCare from '@/assets/icons/ChildCare.svg?react'
import ElderlyCare from '@/assets/icons/ElderlyCare.svg?react'
import Explore from '@/assets/icons/Explore.svg?react'
import Download from '@/assets/icons/Download.svg?react'
import RightArrow from '@/assets/icons/RightArrow.svg?react'
import LottieAnimation from '@/shared/components/common/LottieAnimation'
import { Separator } from '@/shared/components/ui/separator'
import DummyCarousel from '@/assets/images/DummyCarousel.webp'

function HomePage() {
  const navigate = useNavigate()
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [touchStart, setTouchStart] = useState<{
    x: number
    time: number
  } | null>(null)
  const [dragOffset, setDragOffset] = useState(0) // Tracks drag distance in percentage
  const [isRequestingPermission, setIsRequestingPermission] = useState(false)

  const slides = [DummyCarousel, DummyCarousel, DummyCarousel] // Replace with your images

  const nextSlide = useCallback(() => {
    if (!isTransitioning) {
      setIsTransitioning(true)
      setCurrentSlide((prev) => (prev + 1) % slides.length)
      setDragOffset(0) // Reset drag offset after transition
      setTimeout(() => setIsTransitioning(false), 1000)
    }
  }, [slides.length, isTransitioning])

  const prevSlide = useCallback(() => {
    if (!isTransitioning) {
      setIsTransitioning(true)
      setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
      setDragOffset(0) // Reset drag offset after transition
      setTimeout(() => setIsTransitioning(false), 1000)
    }
  }, [slides.length, isTransitioning])

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide()
    }, 4000)
    return () => clearInterval(interval)
  }, [nextSlide])

  const getSlideStyle = (index: number) => {
    const position = index - currentSlide
    const translateX = position * 95 + dragOffset
    const isActive = position === 0
    const scale = isActive ? 1 : 0.85
    const zIndex = isActive ? 10 : 5 - Math.abs(position)
    const opacity = Math.abs(position) <= 1 ? 1 : 0

    return {
      transform: `translateX(${translateX}%) scale(${scale})`,
      zIndex,
      opacity,
      transition:
        isTransitioning && dragOffset === 0
          ? 'all 1s ease-in-out'
          : 'transform 0s, opacity 1s ease-in-out',
    }
  }

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    const touch = e.touches[0]
    setTouchStart({ x: touch.clientX, time: Date.now() })
  }

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!touchStart) return
    const touch = e.touches[0]
    const deltaX = touch.clientX - touchStart.x
    const containerWidth = e.currentTarget.offsetWidth // Width of carousel container
    const offsetPercentage = (deltaX / containerWidth) * 100 // Convert to percentage
    setDragOffset(offsetPercentage)
  }

  const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!touchStart) return

    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - touchStart.x
    const containerWidth = e.currentTarget.offsetWidth
    const offsetPercentage = (deltaX / containerWidth) * 100
    const deltaTime = Date.now() - touchStart.time

    // Determine swipe or snap based on distance and time
    if (Math.abs(deltaX) > 50 && deltaTime < 300) {
      if (deltaX < 0)
        nextSlide() // Swipe left
      else prevSlide() // Swipe right
    } else if (Math.abs(offsetPercentage) > 25) {
      // Snap to next/prev if dragged beyond 25%
      if (offsetPercentage < 0) nextSlide()
      else prevSlide()
    } else {
      // Snap back to current slide
      setIsTransitioning(true)
      setDragOffset(0)
      setTimeout(() => setIsTransitioning(false), 300)
    }

    setTouchStart(null)
  }

  const requestCameraPermission = async (): Promise<boolean> => {
    try {
      setIsRequestingPermission(true)

      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera
        },
      })

      // Stop the stream immediately as we only needed permission
      stream.getTracks().forEach((track) => track.stop())

      console.log('Camera permission granted')
      return true
    } catch (error) {
      console.error('Camera permission denied:', error)

      // You can show a toast or alert here to inform the user
      // For example: toast.error('Cần quyền truy cập camera để quét mã QR')

      return false
    } finally {
      setIsRequestingPermission(false)
    }
  }

  const handleQRScanClick = async () => {
    // Prevent multiple simultaneous requests
    if (isRequestingPermission) return

    const hasPermission = await requestCameraPermission()

    if (hasPermission) {
      navigate('/qrscan', { animate: false, replace: true })
    }
    // If permission denied, do nothing (user stays on HomePage)
  }

  const handleVoucherInputClick = useCallback(() => {
    navigate('/myrewards', {
      animate: false,
      replace: false,
      state: { fromVoucherInput: true },
    })
  }, [navigate])

  const handleLottieClick = useCallback(() => {
    navigate('/myrewards', {
      animate: false,
      replace: false,
      state: { fromVoucherInput: false },
    })
  }, [navigate])

  return (
    <Page className="relative text-black flex flex-col min-h-screen overflow-hidden">
      <div className="absolute inset-0 w-full h-full">
        <HomeBG className="w-full h-full" style={{ objectFit: 'fill' }} />
      </div>

      {/* Content */}
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe pb-safe">
        {/* Top Navigation */}
        <div className="mb-7 h-xs:mb-2 h-sm:mb-4 h-md:mb-4 h-lg:mb-4 h-xl:mb-5 h-2xl:mb-6 flex items-center px-4 h-xs:px-2 space-x-3">
          <Avatar className="w-12 h-12 h-xs:w-8 h-xs:h-8 h-sm:w-8 h-sm:h-8 h-md:w-8 h-md:h-8">
            <AvatarImage src="https://github.com/shadcn.png" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <span className="text-[18px] h-xs:text-[13px] h-sm:text-[16px] h-md:text-[16px] font-bold text-[#FFFFFF]">
            Hôm nay bạn thế nào?
          </span>
        </div>

        {/* Promo Code Input */}
        <div className="px-4 h-xs:px-2 h-sm:px-2">
          <div className="flex space-x-1 mb-1">
            {/* QR Scan Box */}
            <button
              onClick={handleQRScanClick}
              disabled={isRequestingPermission}
              className={`w-12 h-12 h-xs:w-8 h-xs:h-8 h-sm:w-8 h-sm:h-8 bg-[#FFFFFF] border border-[#EBEFF6] rounded-md flex items-center justify-center rounded-tl-2xl ${
                isRequestingPermission
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50'
              } transition-colors`}
            >
              <QRScan className="w-8 h-8 h-xs:w-5 h-xs:h-5 h-sm:w-5 h-sm:h-5" />
            </button>

            {/* Voucher Input Area */}
            <div
              onClick={handleVoucherInputClick}
              className="h-xs:h-8 h-sm:h-8 flex-1 bg-[#FFFFFF] border border-[#EBEFF6] rounded-md flex items-center px-2 rounded-tr-2xl cursor-pointer"
            >
              <img
                src={VoucherImg}
                alt="Voucher"
                className="w-6 h-6 h-xs:w-4 h-xs:h-4 h-sm:w-4 h-sm:h-4"
              />
              <span className="flex-1 pl-2 text-[#9CA3AF] h-xs:text-[13px] h-sm:text-[13px]">
                Nhập mã khuyến mãi
              </span>
              <Separator
                className="h-[60%] mr-2 bg-[#EBEFF6]"
                orientation="vertical"
              />
              <div
                className="relative"
                onClick={(e) => {
                  e.stopPropagation()
                  handleLottieClick()
                }}
              >
                <LottieAnimation />
                <div className="font-bold absolute top-1 right-1 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 h-xs:w-2 h-xs:h-2 h-sm:w-2 h-sm:h-2 rounded-full bg-red-500 text-white text-[10px] h-xs:text-[3px] h-sm:text-[3px] flex items-center justify-center">
                  0
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Location Card */}
        <div className="px-4 mb-8 h-xs:px-2 h-xs:mb-0.5 h-sm:px-2 h-sm:mb-2 h-md:mb-2 h-lg:mb-2 h-xl:mb-2 h-2xl:mb-2 h-3xl:mb-5">
          <div className="bg-[#FFFFFF] rounded-md p-2 border border-[#EBEFF6] rounded-b-2xl">
            <div className="flex items-start space-x-3 h-xs:space-x-2 h-sm:space-x-2">
              <MapHome className="h-20 w-20 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 mt-1 mb-2" />
              <div className="flex flex-col justify-between flex-1 min-h-[80px] h-xs:min-h-[60px] h-sm:min-h-[60px] mt-1">
                <p className="text-[#A5ACB6] text-[14px] h-xs:text-[13px] h-sm:text-[13px] font-medium leading-relaxed">
                  Nhập địa chỉ để tham gia chương trình và nhận ưu đãi gần bạn
                  nhất
                </p>
                <div className="flex justify-end mt-2 mr-2">
                  <button className="translate-y-1 h-xs:-translate-y-1 h-sm:-translate-y-1 h-md:-translate-y-1 p-0 h-auto text-green-600 text-[14px] h-xs:text-[13px] h-sm:text-[13px] font-semibold inline-flex items-center">
                    <MapIcon className="h-5 w-5 h-xs:w-3 h-xs:h-3 h-sm:w-3 h-sm:h-3 mr-2" />
                    Thêm địa chỉ
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Carousel Section */}
        <div className="mb-8 h-xs:mb-0.5 h-sm:mb-2 h-md:mb-2 h-lg:mb-2 h-xl:mb-2 h-2xl:mb-2 h-3xl:mb-5">
          <div
            className="relative min-h-[150px] h-auto h-xs:min-h-[100px] h-sm:min-h-[100px] h-md:min-h-[110px] h-lg:min-h-[110px] h-xl:min-h-[110px] h-2xl:min-h-[110px] h-3xl:min-h-[130px] overflow-hidden"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <div className="absolute inset-0 flex items-center justify-center h-full">
              {slides.map((src, index) => {
                const position = index - currentSlide
                const isActive = position === 0

                return (
                  <div
                    key={index}
                    className={`absolute w-[90%] rounded-2xl overflow-hidden transition-all duration-1000 ease-in-out
        ${
          isActive
            ? 'h-[130px] h-xs:h-[90px] h-sm:h-[90px] h-md:h-[100px] h-lg:h-[100px] h-xl:h-[100px] h-2xl:h-[100px] h-3xl:h-[120px] h-4xl:h-[140px]'
            : 'h-[110px] h-xs:h-[80px] h-sm:h-[80px] h-md:h-[90px] h-lg:h-[90px] h-xl:h-[90px] h-2xl:h-[90px] h-3xl:h-[110px] h-4xl:h-[130px]'
        }`}
                    style={getSlideStyle(index)}
                  >
                    <div className="h-full shadow-lg rounded-lg overflow-hidden">
                      <div className="p-0 h-full">
                        <img
                          src={src}
                          alt={`Carousel image ${index + 1}`}
                          className="w-full h-full object-cover rounded-lg"
                          draggable={false}
                        />
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="px-4 h-xs:px-2 h-sm:px-2 mb-6 h-xs:mb-3 h-sm:mb-3 text-[12px] h-xs:text-[10px] h-sm:text-[10px] text-[#212935] font-medium">
          <div className="grid grid-cols-4 gap-2">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <Cleaning className="scale-110" />
              </div>
              <div className="text-center">
                <p>Cleaning</p>
                <p className="text-[#FF8228]">on-demand</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <OfficeCleaning className="scale-110" />
              </div>
              <div className="text-center">
                <p>Office</p>
                <p>Cleaning</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <Housekeeping className="scale-110" />
              </div>
              <div className="text-center">
                <p>House-</p>
                <p>keeping</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <WashingMachine className="scale-110" />
              </div>
              <div className="text-center">
                <p>W/M Service</p>
              </div>
            </div>
          </div>
        </div>

        {/* Second Services Row */}
        <div className="px-4 h-xs:px-2 h-sm:px-2 mb-6 h-xs:mb-3 h-sm:mb-3 text-[12px] h-xs:text-[10px] h-sm:text-[10px] text-[#212935] font-medium">
          <div className="grid grid-cols-4 gap-2">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <MonthlyCleaning className="scale-110" />
              </div>
              <div className="text-center">
                <p>Cleaning</p>
                <p className="text-[#FF8228]">subscription</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <ChildCare className="scale-110" />
              </div>
              <div className="text-center">
                <p>Child Care</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <ElderlyCare className="scale-110" />
              </div>
              <div className="text-center">
                <p>Elderly Care</p>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="w-12 h-12 h-xs:w-10 h-xs:h-10 h-sm:w-10 h-sm:h-10 rounded-lg flex items-center justify-center">
                <Explore className="scale-110" />
              </div>
              <div className="text-center">
                <p>Explore</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="px-4 h-xs:px-2 h-sm:px-2 mb-4">
          <div className="bg-[#E8F8EF] border border-[#B8E8CC] rounded-xl p-4">
            <div className="flex items-center justify-between">
              {/* Left side */}
              <div className="flex items-center space-x-3">
                <Download className="h-5 w-5 h-xs:h-3 h-xs:w-3 h-sm:h-3 h-sm:w-3 ml-1 -translate-y-0.5" />
                <span className="text-[#1BB55C] font-semibold text-[14px] h-xs:text-[10px] h-sm:text-[12px]">
                  Truy cập ứng dụng bTaskee ngay
                </span>
              </div>

              {/* Right side */}
              <RightArrow className="h-5 w-5 h-xs:h-3 h-xs:w-3 h-sm:h-3 h-sm:w-3" />
            </div>
          </div>
        </div>
      </div>
    </Page>
  )
}

export default HomePage
