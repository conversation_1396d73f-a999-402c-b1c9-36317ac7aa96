import React from 'react'
import Permission from '@/assets/images/Permission.svg?react'

interface PermissionSegmentProps {
  onPermissionComplete: () => void
}

function PermissionSegment({ onPermissionComplete }: PermissionSegmentProps) {
  return (
    // Outer layout: vertical stack, full height
    <div className="flex flex-col h-full w-full items-center relative z-10">
      {/* Top content */}
      <div className="flex-1 flex flex-col items-center justify-center space-y-6 h-sm:space-y-8 h-md:space-y-10 h-md:-translate-y-4 h-lg:space-y-10 h-lg:-translate-y-6 h-xl:space-y-12 h-xl:-translate-y-8 h-2xl:space-y-14 h-2xl:-translate-y-10 h-3xl:space-y-16 h-3xl:-translate-y-12 h-4xl:space-y-20 h-4xl:-translate-y-14 h-5xl:space-y-20 h-5xl:-translate-y-10 h-6xl:space-y-20 h-6xl:-translate-y-20">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-[18px] font-bold text-[#FF8228]">
            bTaskee muốn truy cập
          </h1>
          <h1 className="text-[18px] font-bold text-[#FF8228]">
            thông tin của bạn
          </h1>
          <ul className="max-w-lg text-left text-[#374458] text-[14px] font-medium leading-relaxed pt-1 h-sm:pt-4 h-md:pt-6 h-lg:pt-6 h-xl:pt-6 h-2xl:pt-6 h-3xl:pt-6 h-4xl:pt-6 h-5xl:pt-6 h-6xl:pt-6 list-disc list-outside pl-5 pr-2 h-sm:pl-8 h-sm:pr-3 h-md:pl-8 h-md:pr-3 h-lg:pl-8 h-lg:pr-3 h-xl:pl-8 h-xl:pr-3 h-2xl:pl-8 h-2xl:pr-3 h-3xl:pl-8 h-3xl:pr-3 h-4xl:pl-8 h-4xl:pr-3 h-5xl:pl-8 h-5xl:pr-3 h-6xl:pl-8 h-6xl:pr-3">
            <li>
              Tên, hình ảnh hồ sơ Zalo để xác định và truy cập các tính năng của
              Zalo (bắt buộc)
            </li>
            <li>Số điện thoại của bạn để xác định và liên hệ với tài xế</li>
          </ul>
        </div>

        {/* SVG Illustration */}
        <div className="w-[70%] max-w-lg">
          <Permission className="w-full h-auto" />
        </div>
      </div>

      {/* Bottom button */}
      <div className="w-full max-w-lg py-4">
        <button
          onClick={onPermissionComplete}
          className="w-full bg-[#E4EAF0] text-[#1BB55C] text-[16px] font-medium py-4 rounded-xl font-semibold"
        >
          Đã hiểu
        </button>
      </div>
    </div>
  )
}

export default PermissionSegment
