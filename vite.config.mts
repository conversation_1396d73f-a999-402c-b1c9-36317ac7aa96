import { defineConfig } from 'vite'
import zaloMiniApp from 'zmp-vite-plugin'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'

// https://vitejs.dev/config/
export default () => {
  return defineConfig({
    root: '.',
    base: '',
    plugins: [
      zaloMiniApp(),
      react(),
      svgr({
        svgrOptions: {
          exportType: 'default',
          replaceAttrValues: {
            '#000': 'currentColor',
            '#000000': 'currentColor',
          },
        },
      }),
    ],
    build: {
      assetsInlineLimit: 0,
    },
    resolve: {
      alias: {
        '@': '/src',
      },
    },
  })
}
