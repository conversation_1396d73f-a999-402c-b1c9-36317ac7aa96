1. Presentation Layer

Pages/Screens: Route-level components
Components: Reusable UI components
Layouts: Shared layout structures

2. Business Logic Layer

Hooks: Custom hooks for state and side effects
Services: API communication and external integrations
Stores: Global state management

3. Data Layer

Types: TypeScript interfaces and types
Validators: Input validation schemas
Transformers: Data mapping between API and UI formats

File Structure Template:

src/
├── app/ # App-level configuration
│ ├── providers/ # Context providers, router setup
│ ├── store/ # Global state (Redux/Zustand)
│ └── router/ # Route configuration
│
├── pages/ # Route-level components
│ ├── auth/
│ │ ├── LoginPage.tsx
│ │ └── SignupPage.tsx
│ ├── dashboard/
│ └── profile/
│
├── features/ # Feature-based modules
│ ├── auth/
│ │ ├── components/ # Feature-specific components
│ │ ├── hooks/ # useAuth, useLogin, etc.
│ │ ├── services/ # authService.ts
│ │ ├── types/ # auth.types.ts
│ │ └── index.ts # Public API
│ │
│ ├── user-profile/
│ ├── notifications/
│ └── dashboard/
│
├── shared/ # Shared across features
│ ├── components/  
│ │ ├── ui/ # Base components (Button, Input)
│ │ ├── layout/ # Header, Sidebar, etc.
│ │ └── common/ # LoadingSpinner, ErrorBoundary
│ │
│ ├── hooks/ # Generic hooks
│ │ ├── useApi.ts
│ │ ├── useLocalStorage.ts
│ │ └── useDebounce.ts
│ │
│ ├── services/ # Cross-cutting services
│ │ ├── api/ # HTTP client, interceptors
│ │ ├── storage/ # localStorage, sessionStorage
│ │ └── analytics/ # Tracking, monitoring
│ │
│ ├── utils/ # Pure utility functions
│ │ ├── formatters.ts
│ │ ├── validators.ts
│ │ └── helpers.ts
│ │
│ └── types/ # Global types
│ ├── api.types.ts
│ └── common.types.ts
│
├── assets/ # Static assets
│ ├── images/
│ ├── icons/
│ └── fonts/
│
└── styles/ # Global styles
├── globals.css
├── variables.css
└── components.css
