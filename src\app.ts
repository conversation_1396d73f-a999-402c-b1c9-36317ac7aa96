// src\app.ts
import 'zmp-ui/zaui.css'
import '@/styles/tailwind.scss'
import '@/styles/app.scss'

import React from 'react'
import { createRoot } from 'react-dom/client'

import { App } from '@/app/App'
import { AppConfig } from '@/shared/types'

import appConfig from '../app-config.json'

if (!window.APP_CONFIG) {
  window.APP_CONFIG = appConfig as AppConfig
}

const root = createRoot(document.getElementById('app')!)
root.render(React.createElement(App))

function applySafePaddingFallback() {
  const insetTop =
    parseFloat(
      getComputedStyle(document.documentElement).getPropertyValue(
        'env(safe-area-inset-top)'
      )
    ) || 0
  const insetBottom =
    parseFloat(
      getComputedStyle(document.documentElement).getPropertyValue(
        'env(safe-area-inset-bottom)'
      )
    ) || 0

  if (insetTop === 0) {
    document.body.classList.add('no-safe-top')
  }
  if (insetBottom === 0) {
    document.body.classList.add('no-safe-bottom')
  }
}

window.addEventListener('load', applySafePaddingFallback)
window.addEventListener('resize', applySafePaddingFallback)
