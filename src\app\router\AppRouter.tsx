import React from 'react'
import { AnimationRout<PERSON>, Route, ZMPRouter } from 'zmp-ui'

import {
  IntroPage,
  SignUpPage,
  HomePage,
  QRScanPage,
  MyRewardsPage,
  VoucherDetailPage,
} from '@/pages'

export const AppRouter: React.FC = () => {
  return (
    <ZMPRouter>
      <AnimationRoutes>
        <Route path="/" element={<IntroPage />}></Route>
        <Route path="/signup" element={<SignUpPage />}></Route>
        <Route path="/home" element={<HomePage />}></Route>
        <Route path="/qrscan" element={<QRScanPage />}></Route>
        <Route path="/myrewards" element={<MyRewardsPage />}></Route>
        <Route path="/voucherdetail" element={<VoucherDetailPage />}></Route>
      </AnimationRoutes>
    </ZMPRouter>
  )
}

export default AppRouter
