// Analytics service for tracking and monitoring
interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: Date
}

class AnalyticsService {
  private isEnabled: boolean = true
  private events: AnalyticsEvent[] = []

  constructor() {
    // Initialize analytics service
    this.isEnabled = import.meta.env.PROD
  }

  track(eventName: string, properties?: Record<string, any>): void {
    if (!this.isEnabled) {
      console.log('Analytics (dev mode):', eventName, properties)
      return
    }

    const event: AnalyticsEvent = {
      name: eventName,
      properties,
      timestamp: new Date(),
    }

    this.events.push(event)
    this.sendEvent(event)
  }

  page(pageName: string, properties?: Record<string, any>): void {
    this.track('page_view', {
      page: pageName,
      ...properties,
    })
  }

  identify(userId: string, traits?: Record<string, any>): void {
    this.track('identify', {
      userId,
      traits,
    })
  }

  private sendEvent(event: AnalyticsEvent): void {
    // In a real implementation, this would send to your analytics provider
    // For now, we'll just log it
    console.log('Analytics event:', event)
  }

  getEvents(): AnalyticsEvent[] {
    return [...this.events]
  }

  clearEvents(): void {
    this.events = []
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }
}

export const analyticsService = new AnalyticsService()
export default analyticsService
