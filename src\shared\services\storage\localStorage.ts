// Local storage service with error handling
class LocalStorageService {
  private isAvailable(): boolean {
    try {
      const test = '__localStorage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  setItem<T>(key: string, value: T): boolean {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available')
      return false
    }

    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
      return true
    } catch (error) {
      console.error(`Error setting localStorage item "${key}":`, error)
      return false
    }
  }

  getItem<T>(key: string): T | null {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available')
      return null
    }

    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error(`Error getting localStorage item "${key}":`, error)
      return null
    }
  }

  removeItem(key: string): boolean {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available')
      return false
    }

    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`Error removing localStorage item "${key}":`, error)
      return false
    }
  }

  clear(): boolean {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available')
      return false
    }

    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.error('Error clearing localStorage:', error)
      return false
    }
  }

  getAllKeys(): string[] {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available')
      return []
    }

    try {
      return Object.keys(localStorage)
    } catch (error) {
      console.error('Error getting localStorage keys:', error)
      return []
    }
  }
}

export const localStorageService = new LocalStorageService()
export default localStorageService
